package wish

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/user_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// 邀请同行人列表项
type InviteUserItem struct {
	UserId      uint                     `json:"user_id"`
	Nickname    string                   `json:"nickname"`
	Avatar      string                   `json:"avatar"`
	IsMember    bool                     `json:"is_member"`
	MemberState constmap.WishMemberState `json:"member_state"`
	CanInvite   bool                     `json:"can_invite"`
}

// 邀请同行人列表响应
type InviteListResponse struct {
	List     []InviteUserItem `json:"list"`
	Total    int64            `json:"total"`
	Page     int              `json:"page"`
	PageSize int              `json:"page_size"`
}

// 邀请同行人列表
func InviteList(ctx *gin.Context) (any, error) {
	var in struct {
		WishId uint `form:"wish_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)
	page, pageSize := utils.GetPage(ctx)

	// 1. 验证心愿单是否存在并检查权限
	var wish models.Wish
	if err := db.Take(&wish, in.WishId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单不存在")
	}

	// 2. 验证只有发起人可以查看邀请列表
	if wish.UserId != session.UserId {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "无权限查看邀请列表")
	}

	// 3. 查询所有评论过该心愿单的用户ID（去重）
	var commentUserIds []uint
	err := db.Model(&models.WishComment{}).
		Select("DISTINCT user_id").
		Where("wish_id = ? AND state = ?", in.WishId, constmap.WishCommentStateApproved).
		Pluck("user_id", &commentUserIds)
	if err != nil {
		return nil, utils.NewError(err)
	}

	// 4. 排除发起人自己
	commentUserIds = slice.Filter(commentUserIds, func(index int, userId uint) bool {
		return userId != wish.UserId
	})

	// 5. 分页处理
	total := int64(len(commentUserIds))
	offset := (page - 1) * pageSize
	if offset >= len(commentUserIds) {
		return &InviteListResponse{
			List:     []InviteUserItem{},
			Total:    total,
			Page:     page,
			PageSize: pageSize,
		}, nil
	}

	end := offset + pageSize
	if end > len(commentUserIds) {
		end = len(commentUserIds)
	}
	pagedUserIds := commentUserIds[offset:end]

	// 6. 批量查询用户信息
	userMap := user_biz.LoadUsers(db, pagedUserIds)

	// 7. 批量查询心愿单成员信息
	var members []models.WishMember
	db.Where("wish_id = ? AND user_id IN ?", in.WishId, pagedUserIds).Find(&members)
	memberMap := slice.KeyBy(members, func(member models.WishMember) uint {
		return member.UserId
	})

	// 8. 组装返回数据
	list := make([]InviteUserItem, 0, len(pagedUserIds))
	for _, userId := range pagedUserIds {
		user, userExists := userMap[userId]
		if !userExists {
			continue // 用户不存在，跳过
		}

		member, isMember := memberMap[userId]

		// 判断是否可发起邀请：不在成员列表或者已被踢出的可发起邀请
		canInvite := !isMember || member.State == constmap.WishMemberStateKickOff

		item := InviteUserItem{
			UserId:      userId,
			Nickname:    user.Nickname,
			Avatar:      utils.AvatarUrl(user.Avatar),
			IsMember:    isMember,
			MemberState: 0, // 默认值
			CanInvite:   canInvite,
		}

		// 如果是成员，设置成员状态
		if isMember {
			item.MemberState = member.State
		}

		list = append(list, item)
	}

	return &InviteListResponse{
		List:     list,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	}, nil
}
